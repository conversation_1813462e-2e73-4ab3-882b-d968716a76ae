[pytest]
pythonpath = .
testpaths = tests
python_files = test_*.py *_test.py
python_classes = Test*
python_functions = test_*
addopts =
    --strict-markers
    --strict-config
    --verbose
    --tb=short
    --maxfail=5
    --durations=10
    --cov=.
    --cov-report=term-missing:skip-covered
    --cov-report=html:htmlcov
    --cov-report=xml:coverage.xml
    --cov-report=json:coverage.json
    --cov-fail-under=70
    --cov-branch
    --asyncio-mode=auto
    --disable-warnings
env_files =
    .env.test
env_override_existing_values = 1

markers =
    unit: Unit tests for individual components (isolated, fast, mocked dependencies)
    integration: Integration tests for API endpoints and service interactions
    e2e: End-to-end user journey tests (complete workflows)
    database: Database-related tests (models, queries, transactions)
    api: API endpoint tests (HTTP requests, responses, validation)
    auth: Authentication and authorization tests
    security: Security and vulnerability tests (XSS, SQL injection, etc.)
    ai: AI integration and compliance logic tests
    compliance: Regulatory compliance content validation
    ethical: Ethical AI and bias testing
    performance: Performance and load tests
    slow: Tests that take >5 seconds to run
    memory: Memory usage and leak tests
    concurrency: Concurrent operation tests
    external: Tests that require external services (disabled in CI by default)
    network: Tests requiring network access
    docker: Tests requiring Docker containers
    regression: Regression tests for known bugs
    smoke: Quick smoke tests for basic functionality
    golden: Golden dataset validation tests
    contract: Contract testing for service boundaries
    wip: Work in progress (do not run in CI)
    monitoring: Tests for monitoring and metrics
    sme_validation: SME validation tests
    usability: Usability tests
minversion = 3.11
filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning  
    ignore::UserWarning:google.*
    ignore::RuntimeWarning:asyncio
    ignore::pytest.PytestUnraisableExceptionWarning
    error::UserWarning:tests.*
# timeout = 300
# timeout_method = thread
# dist = worksteal
# numprocesses = auto
norecursedirs = .git .tox venv htmlcov build dist
log_level = INFO
log_cli = true
log_cli_level = WARNING
log_cli_format = %(asctime)s [%(levelname)8s] %(name)s: %(message)s
log_cli_date_format = %Y-%m-%d %H:%M:%S
junit_family = xunit2
junit_logging = all
junit_log_passing_tests = false
# collect_ignore_glob =
    **/node_modules/**
    **/.git/**
