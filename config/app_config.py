"""
Application configuration settings.

This module loads critical configuration values from environment variables
and initializes services like the Fernet cipher for encryption.
"""

import os
from cryptography.fernet import Fernet
from config.logging_config import get_logger

logger = get_logger(__name__)

# --- Fernet Encryption Key --- 
# Load the Fernet key from an environment variable.
# This key MUST be generated by `scripts/generate_fernet_key.py` and kept secret.
FERNET_KEY_ENV_VAR = "FERNET_KEY"
FERNET_KEY = os.getenv(FERNET_KEY_ENV_VAR)

if not FERNET_KEY:
    logger.critical(
        f"{FERNET_KEY_ENV_VAR} environment variable not set. This is required for encrypting credentials. "
        f"Please generate a key using 'python scripts/generate_fernet_key.py' and set it."
    )
    # In a production startup, you might want to raise an error here to prevent the app from starting
    # without encryption capabilities, or fall back to a less secure mode with warnings if absolutely necessary.
    # For now, we'll allow the app to continue but encryption/decryption will fail.
    cipher_suite = None 
elif len(FERNET_KEY.encode()) != 44: # Fernet keys are base64 encoded and 44 bytes long
    logger.critical(
        f"{FERNET_KEY_ENV_VAR} is invalid. It must be a valid Fernet key (44 bytes, base64 encoded). "
        f"Please regenerate the key using 'python scripts/generate_fernet_key.py'."
    )
    cipher_suite = None
else:
    try:
        cipher_suite = Fernet(FERNET_KEY.encode()) # Key needs to be bytes
        logger.info("Fernet cipher suite initialized successfully for credential encryption.")
    except Exception as e:
        logger.critical(f"Failed to initialize Fernet cipher suite with the provided FERNET_KEY: {e}", exc_info=True)
        cipher_suite = None

# --- Other Application Settings (Example) ---
# Add other global configurations here as needed
# API_BASE_URL = os.getenv("API_BASE_URL", "http://localhost:8000")
# DEBUG_MODE = os.getenv("DEBUG_MODE", "False").lower() == "true"

# --- Function to get the cipher --- 
def get_cipher_suite() -> Fernet | None:
    """Returns the initialized Fernet cipher suite."""
    if cipher_suite is None:
        logger.error("Fernet cipher suite is not available. Encryption/decryption will fail.")
    return cipher_suite

# It's good practice to ensure critical environment variables are documented in your README
# and .env.example file.
