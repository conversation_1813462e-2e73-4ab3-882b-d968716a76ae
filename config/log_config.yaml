version: 1
disable_existing_loggers: false

formatters:
  default:
    format: '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
  json:
    class: pythonjsonlogger.jsonlogger.JsonFormatter
    format: '%(asctime)s %(name)s %(levelname)s %(message)s %(module)s %(funcName)s %(lineno)d'
  detailed:
    format: '%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s'

handlers:
  console:
    class: logging.StreamHandler
    level: DEBUG
    formatter: default
    stream: ext://sys.stdout

  file:
    class: logging.handlers.RotatingFileHandler
    level: INFO
    formatter: json
    filename: logs/compliancegpt.log
    maxBytes: 10485760 # 10MB
    backupCount: 5

  error_file:
    class: logging.handlers.RotatingFileHandler
    level: ERROR
    formatter: detailed
    filename: logs/errors.log
    maxBytes: 10485760 # 10MB
    backupCount: 5

loggers:
  api:
    level: DEBUG
    handlers: [console, file]
    propagate: no
  services:
    level: INFO
    handlers: [console, file]
    propagate: no
  database:
    level: WARNING
    handlers: [file]
    propagate: no
  integrations:
    level: INFO
    handlers: [console, file, error_file]
    propagate: no

root:
  level: INFO
  handlers: [console, file, error_file]