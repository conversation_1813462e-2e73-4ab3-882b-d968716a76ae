name: Security Scanning

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]
  schedule:
    - cron: '0 6 * * 1'  # Weekly on Monday at 6 AM
  workflow_dispatch:

env:
  PYTHON_VERSION: '3.11'

jobs:
  static-security-analysis:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}

    - name: Install security tools
      run: |
        python -m pip install --upgrade pip
        pip install bandit safety semgrep pip-audit

    - name: Run Bandit security linter
      run: |
        bandit -r . \
          -f json \
          -o bandit-report.json \
          -ll \
          --exclude ./tests,./venv,./env
      continue-on-error: true

    - name: Run Safety dependency vulnerability scan
      run: |
        safety check \
          --json \
          --output safety-report.json \
          --ignore 70612  # Ignore jinja2 vulnerability (handled in dependencies)
      continue-on-error: true

    - name: Run pip-audit for dependency vulnerabilities
      run: |
        pip-audit \
          --format=json \
          --output=pip-audit-report.json \
          --ignore-vuln GHSA-h75v-3vvj-5mfj  # Ignore specific non-critical vulnerabilities
      continue-on-error: true

    - name: Run Semgrep security scan
      run: |
        semgrep \
          --config=auto \
          --json \
          --output=semgrep-report.json \
          --exclude=tests/ \
          --exclude=.venv/ \
          .
      continue-on-error: true

    - name: Analyze security scan results
      run: |
        python -c "
        import json
        import sys
        from datetime import datetime
        
        # Initialize report
        security_report = {
            'timestamp': datetime.now().isoformat(),
            'git_commit': '${{ github.sha }}',
            'tools_run': [],
            'critical_issues': 0,
            'high_issues': 0,
            'medium_issues': 0,
            'low_issues': 0,
            'total_issues': 0,
            'issues': []
        }
        
        # Analyze Bandit results
        try:
            with open('bandit-report.json', 'r') as f:
                bandit_data = json.load(f)
            
            security_report['tools_run'].append('bandit')
            
            for result in bandit_data.get('results', []):
                severity = result.get('issue_severity', 'LOW').upper()
                security_report['issues'].append({
                    'tool': 'bandit',
                    'severity': severity,
                    'test_id': result.get('test_id'),
                    'test_name': result.get('test_name'),
                    'filename': result.get('filename'),
                    'line_number': result.get('line_number'),
                    'issue_text': result.get('issue_text')
                })
                
                if severity == 'HIGH':
                    security_report['high_issues'] += 1
                elif severity == 'MEDIUM':
                    security_report['medium_issues'] += 1
                else:
                    security_report['low_issues'] += 1
            
            print(f'Bandit: Found {len(bandit_data.get(\"results\", []))} security issues')
            
        except (FileNotFoundError, json.JSONDecodeError) as e:
            print(f'Could not analyze Bandit results: {e}')
        
        # Analyze Safety results
        try:
            with open('safety-report.json', 'r') as f:
                safety_data = json.load(f)
            
            security_report['tools_run'].append('safety')
            
            vulnerabilities = safety_data.get('vulnerabilities', [])
            for vuln in vulnerabilities:
                security_report['issues'].append({
                    'tool': 'safety',
                    'severity': 'HIGH',  # Safety reports are typically high priority
                    'package': vuln.get('package_name'),
                    'vulnerability_id': vuln.get('vulnerability_id'),
                    'advisory': vuln.get('advisory'),
                    'vulnerable_spec': vuln.get('vulnerable_spec')
                })
                security_report['high_issues'] += 1
            
            print(f'Safety: Found {len(vulnerabilities)} dependency vulnerabilities')
            
        except (FileNotFoundError, json.JSONDecodeError) as e:
            print(f'Could not analyze Safety results: {e}')
        
        # Analyze pip-audit results
        try:
            with open('pip-audit-report.json', 'r') as f:
                audit_data = json.load(f)
            
            security_report['tools_run'].append('pip-audit')
            
            vulnerabilities = audit_data.get('vulnerabilities', [])
            for vuln in vulnerabilities:
                security_report['issues'].append({
                    'tool': 'pip-audit',
                    'severity': 'HIGH',
                    'package': vuln.get('package'),
                    'version': vuln.get('version'),
                    'vulnerability_id': vuln.get('id'),
                    'description': vuln.get('description', '')[:200]  # Truncate long descriptions
                })
                security_report['high_issues'] += 1
            
            print(f'pip-audit: Found {len(vulnerabilities)} dependency vulnerabilities')
            
        except (FileNotFoundError, json.JSONDecodeError) as e:
            print(f'Could not analyze pip-audit results: {e}')
        
        # Analyze Semgrep results
        try:
            with open('semgrep-report.json', 'r') as f:
                semgrep_data = json.load(f)
            
            security_report['tools_run'].append('semgrep')
            
            results = semgrep_data.get('results', [])
            for result in results:
                # Map Semgrep severity to our levels
                severity_map = {
                    'ERROR': 'HIGH',
                    'WARNING': 'MEDIUM',
                    'INFO': 'LOW'
                }
                
                severity = severity_map.get(result.get('extra', {}).get('severity', 'INFO'), 'LOW')
                
                security_report['issues'].append({
                    'tool': 'semgrep',
                    'severity': severity,
                    'rule_id': result.get('check_id'),
                    'message': result.get('extra', {}).get('message', ''),
                    'filename': result.get('path'),
                    'line_number': result.get('start', {}).get('line')
                })
                
                if severity == 'HIGH':
                    security_report['high_issues'] += 1
                elif severity == 'MEDIUM':
                    security_report['medium_issues'] += 1
                else:
                    security_report['low_issues'] += 1
            
            print(f'Semgrep: Found {len(results)} security issues')
            
        except (FileNotFoundError, json.JSONDecodeError) as e:
            print(f'Could not analyze Semgrep results: {e}')
        
        # Calculate totals
        security_report['total_issues'] = (
            security_report['critical_issues'] +
            security_report['high_issues'] +
            security_report['medium_issues'] +
            security_report['low_issues']
        )
        
        # Save comprehensive security report
        with open('security_summary_report.json', 'w') as f:
            json.dump(security_report, f, indent=2)
        
        # Print summary
        print('')
        print('🔒 Security Scan Summary')
        print('=' * 40)
        print(f'Tools run: {\" \".join(security_report[\"tools_run\"])}')
        print(f'Total issues: {security_report[\"total_issues\"]}')
        print(f'Critical: {security_report[\"critical_issues\"]}')
        print(f'High: {security_report[\"high_issues\"]}')
        print(f'Medium: {security_report[\"medium_issues\"]}')
        print(f'Low: {security_report[\"low_issues\"]}')
        
        # Show critical and high issues
        critical_high_issues = [
            issue for issue in security_report['issues'] 
            if issue['severity'] in ['CRITICAL', 'HIGH']
        ]
        
        if critical_high_issues:
            print('')
            print('🚨 Critical/High Severity Issues:')
            for issue in critical_high_issues[:10]:  # Show first 10
                tool = issue['tool']
                severity = issue['severity']
                if tool == 'bandit':
                    print(f'  [{severity}] {issue[\"test_name\"]} in {issue[\"filename\"]}:{issue[\"line_number\"]}')
                elif tool in ['safety', 'pip-audit']:
                    print(f'  [{severity}] {issue[\"package\"]} - {issue.get(\"vulnerability_id\", \"\")}')
                elif tool == 'semgrep':
                    print(f'  [{severity}] {issue[\"rule_id\"]} in {issue[\"filename\"]}')
        
        # Determine if we should fail the build
        fail_build = security_report['critical_issues'] > 0 or security_report['high_issues'] > 5
        
        if fail_build:
            print('')
            print('❌ Security scan failed - critical issues or too many high-severity issues found')
            sys.exit(1)
        elif security_report['total_issues'] > 0:
            print('')
            print('⚠️ Security scan completed with warnings - review and address security issues')
        else:
            print('')
            print('✅ Security scan passed - no significant issues found')
        "

    - name: Upload security scan results
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: security-scan-results
        path: |
          bandit-report.json
          safety-report.json
          pip-audit-report.json
          semgrep-report.json
          security_summary_report.json

  secrets-scan:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      with:
        fetch-depth: 0  # Full history for secret scanning

    - name: Install TruffleHog
      run: |
        curl -sSfL https://raw.githubusercontent.com/trufflesecurity/trufflehog/main/scripts/install.sh | sh -s -- -b /usr/local/bin

    - name: Run TruffleHog secret scan
      run: |
        trufflehog git file://. \
          --json \
          --no-update \
          > trufflehog-results.json
      continue-on-error: true

    - name: Analyze secret scan results
      run: |
        python -c "
        import json
        import sys
        
        try:
            with open('trufflehog-results.json', 'r') as f:
                content = f.read().strip()
                
            if not content:
                print('✅ No secrets detected by TruffleHog')
                sys.exit(0)
            
            # Parse line-delimited JSON
            secrets = []
            for line in content.split('\n'):
                if line.strip():
                    try:
                        secrets.append(json.loads(line))
                    except json.JSONDecodeError:
                        continue
            
            if not secrets:
                print('✅ No secrets detected by TruffleHog')
                sys.exit(0)
            
            print(f'🚨 {len(secrets)} potential secrets detected!')
            
            # Group by detector type
            by_detector = {}
            for secret in secrets:
                detector = secret.get('DetectorName', 'Unknown')
                if detector not in by_detector:
                    by_detector[detector] = []
                by_detector[detector].append(secret)
            
            for detector, findings in by_detector.items():
                print(f'  {detector}: {len(findings)} findings')
                for finding in findings[:3]:  # Show first 3 of each type
                    source_name = finding.get('SourceMetadata', {}).get('Data', {}).get('Git', {}).get('file', 'Unknown')
                    print(f'    - File: {source_name}')
            
            # Fail if high-confidence secrets found
            high_confidence = [s for s in secrets if s.get('Verified', False)]
            if high_confidence:
                print(f'❌ {len(high_confidence)} verified secrets found - failing build')
                sys.exit(1)
            else:
                print('⚠️ Potential secrets found but not verified - review manually')
                
        except FileNotFoundError:
            print('✅ No secrets scan results found')
        except Exception as e:
            print(f'Error analyzing secrets scan: {e}')
        "

    - name: Upload secrets scan results
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: secrets-scan-results
        path: |
          trufflehog-results.json

  docker-security-scan:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3

    - name: Build Docker image for scanning
      run: |
        docker build -t compliancegpt:security-scan .

    - name: Run Trivy vulnerability scanner
      uses: aquasecurity/trivy-action@master
      with:
        image-ref: 'compliancegpt:security-scan'
        format: 'json'
        output: 'trivy-results.json'

    - name: Analyze Docker security scan
      run: |
        python -c "
        import json
        import sys
        
        try:
            with open('trivy-results.json', 'r') as f:
                trivy_data = json.load(f)
            
            # Count vulnerabilities by severity
            vulnerabilities = {
                'CRITICAL': 0,
                'HIGH': 0,
                'MEDIUM': 0,
                'LOW': 0,
                'UNKNOWN': 0
            }
            
            results = trivy_data.get('Results', [])
            for result in results:
                for vuln in result.get('Vulnerabilities', []):
                    severity = vuln.get('Severity', 'UNKNOWN')
                    vulnerabilities[severity] = vulnerabilities.get(severity, 0) + 1
            
            total_vulns = sum(vulnerabilities.values())
            
            print('🐳 Docker Security Scan Results')
            print('=' * 35)
            print(f'Total vulnerabilities: {total_vulns}')
            for severity, count in vulnerabilities.items():
                if count > 0:
                    print(f'{severity}: {count}')
            
            # Check if we should fail
            critical_high = vulnerabilities['CRITICAL'] + vulnerabilities['HIGH']
            if critical_high > 10:  # More than 10 critical/high vulnerabilities
                print(f'❌ Too many critical/high vulnerabilities: {critical_high}')
                sys.exit(1)
            elif critical_high > 0:
                print(f'⚠️ {critical_high} critical/high vulnerabilities found - review required')
            else:
                print('✅ No critical or high vulnerabilities found')
                
        except FileNotFoundError:
            print('⚠️ Trivy results not found')
        except Exception as e:
            print(f'Error analyzing Docker scan: {e}')
        "

    - name: Upload Docker security scan results
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: docker-security-results
        path: |
          trivy-results.json

  security-tests:
    runs-on: ubuntu-latest
    
    services:
      postgres:
        image: postgres:13
        env:
          POSTGRES_USER: postgres
          POSTGRES_PASSWORD: test_password
          POSTGRES_DB: compliance_test
        ports:
          - 5432:5432
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

      redis:
        image: redis:7-alpine
        ports:
          - 6379:6379
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}
        cache: 'pip'

    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt

    - name: Set up test database
      run: |
        python -c "
        from database.db_setup import init_db
        init_db()
        print('Test database initialized')
        "

    - name: Run security tests
      run: |
        pytest tests/security/ \
          -v \
          --tb=short \
          --junit-xml=security-test-results.xml \
          --cov=api \
          --cov=services \
          --cov-report=xml:security-coverage.xml

    - name: Upload security test results
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: security-test-results
        path: |
          security-test-results.xml
          security-coverage.xml

  security-summary:
    runs-on: ubuntu-latest
    needs: [static-security-analysis, secrets-scan, docker-security-scan, security-tests]
    if: always()
    
    steps:
    - name: Download all security artifacts
      uses: actions/download-artifact@v3
      with:
        path: security-artifacts/

    - name: Generate comprehensive security report
      run: |
        python -c "
        import json
        import os
        from datetime import datetime
        import xml.etree.ElementTree as ET
        
        # Initialize comprehensive report
        report = {
            'timestamp': datetime.now().isoformat(),
            'git_commit': '${{ github.sha }}',
            'workflow_run_id': '${{ github.run_id }}',
            'scans_completed': [],
            'total_issues': 0,
            'critical_issues': 0,
            'high_issues': 0,
            'medium_issues': 0,
            'low_issues': 0,
            'recommendations': [],
            'overall_status': 'UNKNOWN'
        }
        
        # Process static analysis results
        static_file = 'security-artifacts/security-scan-results/security_summary_report.json'
        if os.path.exists(static_file):
            with open(static_file, 'r') as f:
                static_data = json.load(f)
            
            report['scans_completed'].extend(static_data.get('tools_run', []))
            report['total_issues'] += static_data.get('total_issues', 0)
            report['critical_issues'] += static_data.get('critical_issues', 0)
            report['high_issues'] += static_data.get('high_issues', 0)
            report['medium_issues'] += static_data.get('medium_issues', 0)
            report['low_issues'] += static_data.get('low_issues', 0)
        
        # Process security test results
        test_file = 'security-artifacts/security-test-results/security-test-results.xml'
        if os.path.exists(test_file):
            try:
                tree = ET.parse(test_file)
                root = tree.getroot()
                
                tests = int(root.get('tests', 0))
                failures = int(root.get('failures', 0))
                errors = int(root.get('errors', 0))
                
                report['scans_completed'].append('security-tests')
                
                if failures > 0 or errors > 0:
                    report['high_issues'] += failures + errors
                    report['total_issues'] += failures + errors
                    
            except Exception as e:
                print(f'Error parsing security test results: {e}')
        
        # Generate recommendations
        if report['critical_issues'] > 0:
            report['recommendations'].append({
                'priority': 'CRITICAL',
                'action': 'Address critical security vulnerabilities immediately',
                'details': f'{report[\"critical_issues\"]} critical issues found'
            })
        
        if report['high_issues'] > 5:
            report['recommendations'].append({
                'priority': 'HIGH',
                'action': 'Review and remediate high-severity security issues',
                'details': f'{report[\"high_issues\"]} high-severity issues found'
            })
        
        if 'bandit' in report['scans_completed']:
            report['recommendations'].append({
                'priority': 'MEDIUM',
                'action': 'Review Bandit findings for code security improvements',
                'details': 'Static code analysis completed'
            })
        
        # Determine overall status
        if report['critical_issues'] > 0:
            report['overall_status'] = 'CRITICAL'
        elif report['high_issues'] > 10:
            report['overall_status'] = 'HIGH_RISK'
        elif report['high_issues'] > 0 or report['medium_issues'] > 20:
            report['overall_status'] = 'MEDIUM_RISK'
        elif report['total_issues'] > 0:
            report['overall_status'] = 'LOW_RISK'
        else:
            report['overall_status'] = 'SECURE'
        
        # Save comprehensive report
        with open('comprehensive_security_report.json', 'w') as f:
            json.dump(report, f, indent=2)
        
        # Print summary
        print('🔒 Comprehensive Security Report')
        print('=' * 45)
        print(f'Overall Status: {report[\"overall_status\"]}')
        print(f'Scans Completed: {\" \".join(set(report[\"scans_completed\"]))}')
        print(f'Total Issues: {report[\"total_issues\"]}')
        print(f'Critical: {report[\"critical_issues\"]}')
        print(f'High: {report[\"high_issues\"]}')
        print(f'Medium: {report[\"medium_issues\"]}')
        print(f'Low: {report[\"low_issues\"]}')
        
        if report['recommendations']:
            print('')
            print('Recommendations:')
            for rec in report['recommendations']:
                print(f'  [{rec[\"priority\"]}] {rec[\"action\"]}')
        
        # Exit with appropriate code
        if report['overall_status'] in ['CRITICAL', 'HIGH_RISK']:
            print('')
            print('❌ Security scan failed - critical issues found')
            exit(1)
        elif report['overall_status'] in ['MEDIUM_RISK', 'LOW_RISK']:
            print('')
            print('⚠️ Security scan completed with warnings')
        else:
            print('')
            print('✅ Security scan passed - system appears secure')
        "

    - name: Upload comprehensive security report
      uses: actions/upload-artifact@v3
      with:
        name: comprehensive-security-report
        path: comprehensive_security_report.json