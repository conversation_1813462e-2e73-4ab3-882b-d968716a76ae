name: Deployment Pipeline

on:
  workflow_run:
    workflows: ["ComplianceGPT Test Suite"]
    types:
      - completed
    branches: [main]
  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment to deploy to'
        required: true
        type: choice
        options:
        - staging
        - production
      skip_tests:
        description: 'Skip test validation'
        required: false
        type: boolean
        default: false

env:
  PYTHON_VERSION: '3.11'
  DOCKER_REGISTRY: ghcr.io
  IMAGE_NAME: compliancegpt

jobs:
  validate-deployment:
    runs-on: ubuntu-latest
    if: github.event.workflow_run.conclusion == 'success' || github.event_name == 'workflow_dispatch'
    
    outputs:
      deploy_environment: ${{ steps.determine-env.outputs.environment }}
      should_deploy: ${{ steps.determine-env.outputs.should_deploy }}
    
    steps:
    - name: Determine deployment environment
      id: determine-env
      run: |
        if [ "${{ github.event_name }}" = "workflow_dispatch" ]; then
          echo "environment=${{ github.event.inputs.environment }}" >> $GITHUB_OUTPUT
          echo "should_deploy=true" >> $GITHUB_OUTPUT
        elif [ "${{ github.ref }}" = "refs/heads/main" ]; then
          echo "environment=staging" >> $GITHUB_OUTPUT
          echo "should_deploy=true" >> $GITHUB_OUTPUT
        else
          echo "should_deploy=false" >> $GITHUB_OUTPUT
        fi
    
    - name: Validate test results
      if: github.event.inputs.skip_tests != 'true'
      run: |
        echo "Validating test suite results..."
        if [ "${{ github.event.workflow_run.conclusion }}" != "success" ]; then
          echo "❌ Test suite did not pass - blocking deployment"
          exit 1
        fi
        echo "✅ Test suite passed - proceeding with deployment"

  build-and-push:
    runs-on: ubuntu-latest
    needs: validate-deployment
    if: needs.validate-deployment.outputs.should_deploy == 'true'
    
    outputs:
      image_tag: ${{ steps.meta.outputs.tags }}
      image_digest: ${{ steps.build.outputs.digest }}
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3

    - name: Log in to Container Registry
      uses: docker/login-action@v3
      with:
        registry: ${{ env.DOCKER_REGISTRY }}
        username: ${{ github.actor }}
        password: ${{ secrets.GITHUB_TOKEN }}

    - name: Extract metadata
      id: meta
      uses: docker/metadata-action@v5
      with:
        images: ${{ env.DOCKER_REGISTRY }}/${{ github.repository }}/${{ env.IMAGE_NAME }}
        tags: |
          type=ref,event=branch
          type=ref,event=pr
          type=sha,prefix={{branch}}-
          type=raw,value=latest,enable={{is_default_branch}}

    - name: Build and push Docker image
      id: build
      uses: docker/build-push-action@v5
      with:
        context: .
        push: true
        tags: ${{ steps.meta.outputs.tags }}
        labels: ${{ steps.meta.outputs.labels }}
        cache-from: type=gha
        cache-to: type=gha,mode=max
        platforms: linux/amd64,linux/arm64

    - name: Generate SBOM
      uses: anchore/sbom-action@v0
      with:
        image: ${{ steps.meta.outputs.tags }}
        format: spdx-json
        output-file: sbom.spdx.json

    - name: Upload SBOM
      uses: actions/upload-artifact@v3
      with:
        name: sbom
        path: sbom.spdx.json

  deploy-staging:
    runs-on: ubuntu-latest
    needs: [validate-deployment, build-and-push]
    if: needs.validate-deployment.outputs.deploy_environment == 'staging'
    environment: staging
    
    steps:
    - name: Deploy to staging
      run: |
        echo "🚀 Deploying to staging environment..."
        echo "Image: ${{ needs.build-and-push.outputs.image_tag }}"
        echo "Digest: ${{ needs.build-and-push.outputs.image_digest }}"
        
        # In a real deployment, this would:
        # - Update Kubernetes manifests
        # - Apply configuration changes
        # - Trigger deployment pipeline
        # - Wait for health checks
        
        echo "✅ Staging deployment completed"

    - name: Run smoke tests
      run: |
        echo "🧪 Running smoke tests against staging..."
        
        # In a real deployment, this would:
        # - Wait for deployment to be ready
        # - Run health checks
        # - Test critical user paths
        # - Validate API endpoints
        
        echo "✅ Smoke tests passed"

    - name: Update deployment status
      run: |
        echo "📊 Staging deployment summary:"
        echo "- Environment: staging"
        echo "- Image: ${{ needs.build-and-push.outputs.image_tag }}"
        echo "- Deployed at: $(date -Iseconds)"
        echo "- Status: SUCCESS"

  deploy-production:
    runs-on: ubuntu-latest
    needs: [validate-deployment, build-and-push]
    if: needs.validate-deployment.outputs.deploy_environment == 'production'
    environment: production
    
    steps:
    - name: Pre-deployment checks
      run: |
        echo "🔍 Running pre-deployment checks for production..."
        
        # In a real deployment, this would:
        # - Verify staging deployment success
        # - Check database migration status
        # - Validate configuration
        # - Ensure backup procedures
        
        echo "✅ Pre-deployment checks passed"

    - name: Deploy to production
      run: |
        echo "🚀 Deploying to production environment..."
        echo "Image: ${{ needs.build-and-push.outputs.image_tag }}"
        echo "Digest: ${{ needs.build-and-push.outputs.image_digest }}"
        
        # In a real deployment, this would:
        # - Execute blue-green deployment
        # - Update load balancer configuration
        # - Run database migrations
        # - Update monitoring alerts
        
        echo "✅ Production deployment completed"

    - name: Post-deployment verification
      run: |
        echo "✅ Running post-deployment verification..."
        
        # In a real deployment, this would:
        # - Monitor application metrics
        # - Verify all services are healthy
        # - Test critical business functions
        # - Check error rates and performance
        
        echo "✅ Post-deployment verification passed"

    - name: Update deployment status
      run: |
        echo "📊 Production deployment summary:"
        echo "- Environment: production"
        echo "- Image: ${{ needs.build-and-push.outputs.image_tag }}"
        echo "- Deployed at: $(date -Iseconds)"
        echo "- Status: SUCCESS"

  security-validation:
    runs-on: ubuntu-latest
    needs: [build-and-push]
    
    steps:
    - name: Run container security scan
      uses: aquasecurity/trivy-action@master
      with:
        image-ref: ${{ needs.build-and-push.outputs.image_tag }}
        format: 'sarif'
        output: 'trivy-results.sarif'

    - name: Upload Trivy scan results
      uses: github/codeql-action/upload-sarif@v2
      with:
        sarif_file: 'trivy-results.sarif'

    - name: Validate security compliance
      run: |
        echo "🔒 Validating security compliance for deployment..."
        echo "✅ Container security scan completed"

  rollback-capability:
    runs-on: ubuntu-latest
    needs: [deploy-staging, deploy-production]
    if: failure()
    
    steps:
    - name: Prepare rollback
      run: |
        echo "⚠️ Deployment failure detected - preparing rollback capability"
        
        # In a real deployment, this would:
        # - Identify previous stable version
        # - Prepare rollback scripts
        # - Create rollback job/workflow
        # - Set up monitoring for rollback triggers
        
        echo "🔄 Rollback capability prepared"