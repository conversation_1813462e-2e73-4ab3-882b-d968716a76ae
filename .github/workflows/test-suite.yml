name: ComplianceGPT Test Suite

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]
  workflow_dispatch:

env:
  PYTHON_VERSION: '3.11'
  DATABASE_URL: postgresql://postgres:test_password@localhost:5432/compliance_test
  REDIS_URL: redis://localhost:6379/0
  GOOGLE_API_KEY: ${{ secrets.GOOGLE_API_KEY }}
  SECRET_KEY: test_secret_key_for_ci
  ENVIRONMENT: test

jobs:
  test:
    runs-on: ubuntu-latest
    
    services:
      postgres:
        image: postgres:13
        env:
          POSTGRES_USER: postgres
          POSTGRES_PASSWORD: test_password
          POSTGRES_DB: compliance_test
        ports:
          - 5432:5432
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

      redis:
        image: redis:7-alpine
        ports:
          - 6379:6379
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}
        cache: 'pip'

    - name: Install system dependencies
      run: |
        sudo apt-get update
        sudo apt-get install -y postgresql-client

    - name: Install Python dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install pytest-cov pytest-benchmark pytest-mock pytest-asyncio

    - name: Wait for services
      run: |
        # Wait for PostgreSQL
        until pg_isready -h localhost -p 5432 -U postgres; do
          echo "Waiting for PostgreSQL..."
          sleep 2
        done
        
        # Wait for Redis
        until redis-cli -h localhost -p 6379 ping; do
          echo "Waiting for Redis..."
          sleep 2
        done

    - name: Set up database
      run: |
        # Create test database tables
        python -c "
        from database.db_setup import init_db
        init_db()
        print('Database initialized successfully')
        "

    - name: Run linting and formatting checks
      run: |
        # Install linting tools
        pip install flake8 black isort
        
        # Check code formatting
        black --check --diff .
        
        # Check import sorting
        isort --check-only --diff .
        
        # Run flake8 linting
        flake8 . --count --select=E9,F63,F7,F82 --show-source --statistics
        flake8 . --count --exit-zero --max-complexity=10 --max-line-length=127 --statistics

    - name: Run unit tests
      run: |
        pytest tests/unit/ \
          -v \
          --cov=services \
          --cov=database \
          --cov-report=xml \
          --cov-report=html \
          --cov-report=term-missing \
          --cov-fail-under=70 \
          --junit-xml=junit-unit.xml

    - name: Run integration tests
      run: |
        pytest tests/integration/ \
          -v \
          --cov=api \
          --cov-append \
          --cov-report=xml \
          --cov-report=html \
          --cov-report=term-missing \
          --junit-xml=junit-integration.xml

    - name: Run security tests
      run: |
        pytest tests/security/ \
          -v \
          --junit-xml=junit-security.xml

    - name: Run AI tests
      run: |
        pytest tests/ai/ \
          -v \
          --junit-xml=junit-ai.xml
      if: env.GOOGLE_API_KEY != ''

    - name: Run E2E tests
      run: |
        # Start application in background
        python main.py &
        APP_PID=$!
        
        # Wait for application to start
        sleep 10
        
        # Run E2E tests
        pytest tests/e2e/ \
          -v \
          --junit-xml=junit-e2e.xml
        
        # Stop application
        kill $APP_PID || true

    - name: Upload coverage reports
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage.xml
        flags: unittests
        name: codecov-umbrella
        fail_ci_if_error: false

    - name: Upload test results
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: test-results
        path: |
          junit-*.xml
          htmlcov/
          coverage.xml

    - name: Generate test summary
      if: always()
      run: |
        echo "## Test Results Summary" >> $GITHUB_STEP_SUMMARY
        echo "" >> $GITHUB_STEP_SUMMARY
        
        # Parse test results
        if [ -f "junit-unit.xml" ]; then
          UNIT_TESTS=$(grep -o 'tests="[^"]*"' junit-unit.xml | cut -d'"' -f2)
          UNIT_FAILURES=$(grep -o 'failures="[^"]*"' junit-unit.xml | cut -d'"' -f2)
          echo "- **Unit Tests**: $UNIT_TESTS total, $UNIT_FAILURES failures" >> $GITHUB_STEP_SUMMARY
        fi
        
        if [ -f "junit-integration.xml" ]; then
          INTEGRATION_TESTS=$(grep -o 'tests="[^"]*"' junit-integration.xml | cut -d'"' -f2)
          INTEGRATION_FAILURES=$(grep -o 'failures="[^"]*"' junit-integration.xml | cut -d'"' -f2)
          echo "- **Integration Tests**: $INTEGRATION_TESTS total, $INTEGRATION_FAILURES failures" >> $GITHUB_STEP_SUMMARY
        fi
        
        if [ -f "junit-security.xml" ]; then
          SECURITY_TESTS=$(grep -o 'tests="[^"]*"' junit-security.xml | cut -d'"' -f2)
          SECURITY_FAILURES=$(grep -o 'failures="[^"]*"' junit-security.xml | cut -d'"' -f2)
          echo "- **Security Tests**: $SECURITY_TESTS total, $SECURITY_FAILURES failures" >> $GITHUB_STEP_SUMMARY
        fi
        
        if [ -f "coverage.xml" ]; then
          COVERAGE=$(grep -o 'line-rate="[^"]*"' coverage.xml | head -1 | cut -d'"' -f2)
          COVERAGE_PERCENT=$(echo "$COVERAGE * 100" | bc -l | xargs printf "%.1f")
          echo "- **Code Coverage**: $COVERAGE_PERCENT%" >> $GITHUB_STEP_SUMMARY
        fi

  performance:
    runs-on: ubuntu-latest
    if: github.event_name == 'workflow_dispatch' || contains(github.event.head_commit.message, '[perf]')
    
    services:
      postgres:
        image: postgres:13
        env:
          POSTGRES_USER: postgres
          POSTGRES_PASSWORD: test_password
          POSTGRES_DB: compliance_test
        ports:
          - 5432:5432
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

      redis:
        image: redis:7-alpine
        ports:
          - 6379:6379
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}
        cache: 'pip'

    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install pytest-benchmark locust psutil pandas

    - name: Set up database
      run: |
        python -c "
        from database.db_setup import init_db
        init_db()
        print('Database initialized successfully')
        "

    - name: Run performance benchmarks
      run: |
        pytest tests/performance/test_api_performance.py \
          --benchmark-only \
          --benchmark-json=api_benchmark.json \
          --benchmark-sort=mean

    - name: Run database performance tests
      run: |
        pytest tests/performance/test_database_performance.py \
          --benchmark-only \
          --benchmark-json=db_benchmark.json \
          --benchmark-sort=mean

    - name: Upload performance results
      uses: actions/upload-artifact@v3
      with:
        name: performance-results
        path: |
          *_benchmark.json

  security-scan:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}

    - name: Install security tools
      run: |
        python -m pip install --upgrade pip
        pip install bandit safety semgrep

    - name: Run Bandit security scan
      run: |
        bandit -r . -f json -o bandit-report.json
      continue-on-error: true

    - name: Run Safety dependency scan
      run: |
        safety check --json --output safety-report.json
      continue-on-error: true

    - name: Run Semgrep security scan
      run: |
        semgrep --config=auto --json --output=semgrep-report.json .
      continue-on-error: true

    - name: Upload security scan results
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: security-reports
        path: |
          bandit-report.json
          safety-report.json
          semgrep-report.json

  dependency-check:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}

    - name: Install pip-audit
      run: |
        python -m pip install --upgrade pip
        pip install pip-audit

    - name: Run dependency audit
      run: |
        pip-audit --format=json --output=pip-audit-report.json
      continue-on-error: true

    - name: Check for outdated packages
      run: |
        pip install pip-check
        pip-check --format=json > outdated-packages.json
      continue-on-error: true

    - name: Upload dependency check results
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: dependency-reports
        path: |
          pip-audit-report.json
          outdated-packages.json

  docker-build:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3

    - name: Build Docker image
      run: |
        docker build -t compliancegpt:test .

    - name: Test Docker image
      run: |
        # Run basic smoke test on Docker image
        docker run --rm compliancegpt:test python -c "
        import sys
        print(f'Python version: {sys.version}')
        
        # Test imports
        try:
            from api.main import app
            from database.db_setup import init_db
            print('✅ Application imports successful')
        except Exception as e:
            print(f'❌ Import error: {e}')
            sys.exit(1)
        "

  code-quality:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}

    - name: Install quality tools
      run: |
        python -m pip install --upgrade pip
        pip install pylint mypy black isort radon

    - name: Run type checking
      run: |
        mypy . --ignore-missing-imports --no-strict-optional
      continue-on-error: true

    - name: Run code complexity analysis
      run: |
        radon cc . --json > complexity-report.json
        radon mi . --json > maintainability-report.json
      continue-on-error: true

    - name: Check code formatting
      run: |
        black --check --diff . > formatting-report.txt 2>&1 || true
        isort --check-only --diff . >> formatting-report.txt 2>&1 || true

    - name: Upload code quality reports
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: code-quality-reports
        path: |
          complexity-report.json
          maintainability-report.json
          formatting-report.txt