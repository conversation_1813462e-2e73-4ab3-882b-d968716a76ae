# Global code owners
* @project-maintainers

# Security-related files require security team review
/tests/security/ @security-team
/.github/workflows/security-scan.yml @security-team
/api/auth/ @security-team

# Performance testing requires performance team review
/tests/performance/ @performance-team
/.github/workflows/performance-tests.yml @performance-team

# Database changes require DBA review
/database/ @database-team
/migrations/ @database-team

# CI/CD pipeline changes require DevOps review
/.github/workflows/ @devops-team

# Core API changes require backend team review
/api/ @backend-team
/services/ @backend-team

# Testing framework changes require QA review
/tests/ @qa-team
/pytest.ini @qa-team
/conftest.py @qa-team