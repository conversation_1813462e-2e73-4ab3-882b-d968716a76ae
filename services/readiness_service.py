from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from uuid import UUID

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select

from database.models import (
    BusinessProfile as BusinessProfileModel,
    ComplianceFramework as ComplianceFrameworkModel,
    Evidence as EvidenceItemModel,
    GeneratedPolicy as GeneratedPolicyModel,
    ImplementationPlan as ImplementationPlanModel,
    ReadinessAssessment as ReadinessAssessmentModel,
    User as UserModel
)

# NOTE: Assuming these helper functions are CPU-bound and do not need to be async.
# Their definitions would be here.
def calculate_policy_score(policies: List[GeneratedPolicyModel]) -> float:
    # Placeholder for actual calculation logic
    if not policies: return 0.0
    return 75.0

def calculate_implementation_score(plans: List[ImplementationPlanModel]) -> float:
    # Placeholder for actual calculation logic
    if not plans: return 0.0
    return 60.0

def calculate_evidence_score(evidence: List[EvidenceItemModel]) -> float:
    # Placeholder for actual calculation logic
    if not evidence: return 0.0
    return 80.0

def analyze_readiness_details(policy_score, implementation_score, evidence_score):
    # Placeholder for detailed analysis
    return {
        "priority_actions": [{"action": "Improve policy coverage", "urgency": "high", "impact": "high"}],
        "quick_wins": [{"action": "Upload missing evidence for control X", "effort": "low"}],
        "estimated_readiness_date": datetime.utcnow() + timedelta(days=90)
    }


async def generate_compliance_report(
    user: UserModel,
    framework: str,
    report_type: str,
    format: str,
    include_evidence: bool,
    include_recommendations: bool,
) -> bytes | Dict[str, Any]:
    """
    Generates a compliance report based on the latest readiness assessment.
    Placeholder implementation.
    """
    # In a real implementation, this would query the latest assessment,
    # gather data, and use a reporting library (like WeasyPrint or reportlab for PDF)
    # or just format a dictionary for JSON output.
    if format == "pdf":
        # Placeholder for PDF generation
        return b"%PDF-1.4...minimal pdf..."
    else: # json
        return {
            "report_metadata": {
                "user_id": user.id,
                "framework": framework,
                "report_type": report_type,
                "generated_at": datetime.utcnow().isoformat(),
            },
            "summary": "This is a placeholder compliance report.",
            "recommendations": "Implement all the things." if include_recommendations else "N/A",
            "evidence": "Evidence included." if include_evidence else "N/A",
        }


async def get_historical_assessments(
    db: AsyncSession, user: UserModel, business_profile_id: UUID
) -> List[ReadinessAssessmentModel]:
    """Placeholder for retrieving historical readiness assessments."""
    # In a real implementation, this would query the database for assessments
    # associated with the user and business profile.
    return []

async def generate_readiness_assessment(
    db: AsyncSession,
    user: UserModel,
    framework_id: UUID,
    assessment_type: str = "full"
) -> ReadinessAssessmentModel:
    """Generate a comprehensive compliance readiness assessment asynchronously."""

    # Get business profile
    profile_stmt = select(BusinessProfileModel).where(BusinessProfileModel.user_id == user.id)
    profile_res = await db.execute(profile_stmt)
    profile = profile_res.scalars().first()
    if not profile:
        raise ValueError("Business profile not found")

    # Get compliance framework
    framework_stmt = select(ComplianceFrameworkModel).where(ComplianceFrameworkModel.id == framework_id)
    framework_res = await db.execute(framework_stmt)
    framework = framework_res.scalars().first()
    if not framework:
        raise ValueError("Compliance framework not found")

    # Get related artifacts
    policies_stmt = select(GeneratedPolicyModel).where(
        GeneratedPolicyModel.user_id == user.id, GeneratedPolicyModel.framework_id == framework_id
    )
    policies_res = await db.execute(policies_stmt)
    policies = policies_res.scalars().all()

    impl_plans_stmt = select(ImplementationPlanModel).where(
        ImplementationPlanModel.user_id == user.id, ImplementationPlanModel.framework_id == framework_id
    )
    impl_plans_res = await db.execute(impl_plans_stmt)
    implementation_plans = impl_plans_res.scalars().all()

    evidence_stmt = select(EvidenceItemModel).where(
        EvidenceItemModel.user_id == user.id, EvidenceItemModel.framework_id == framework_id
    )
    evidence_res = await db.execute(evidence_stmt)
    evidence_items = evidence_res.scalars().all()

    # Calculate scores (using synchronous helpers)
    policy_score = calculate_policy_score(policies)
    implementation_score = calculate_implementation_score(implementation_plans)
    evidence_score = calculate_evidence_score(evidence_items)

    overall_score = (policy_score + implementation_score + evidence_score) / 3

    # Analyze for actions and readiness date
    analysis = analyze_readiness_details(policy_score, implementation_score, evidence_score)

    # Create and save assessment
    new_assessment = ReadinessAssessmentModel(
        user_id=user.id,
        framework_id=framework_id,
        overall_score=overall_score,
        policy_score=policy_score,
        implementation_score=implementation_score,
        evidence_score=evidence_score,
        priority_actions=analysis["priority_actions"],
        quick_wins=analysis["quick_wins"],
        estimated_readiness_date=analysis["estimated_readiness_date"],
        status="completed"
    )

    db.add(new_assessment)
    await db.commit()
    await db.refresh(new_assessment)

    return new_assessment

async def get_readiness_dashboard(
    db: AsyncSession, user: UserModel
) -> Dict[str, Any]:
    """Get a high-level readiness dashboard for the user."""

    # Get all frameworks and their latest assessments for the user
    frameworks_stmt = select(ComplianceFrameworkModel)
    frameworks_res = await db.execute(frameworks_stmt)
    all_frameworks = frameworks_res.scalars().all()

    framework_data = []
    for fw in all_frameworks:
        assessment_stmt = select(ReadinessAssessmentModel).where(
            ReadinessAssessmentModel.user_id == user.id,
            ReadinessAssessmentModel.framework_id == fw.id
        ).order_by(ReadinessAssessmentModel.created_at.desc())
        assessment_res = await db.execute(assessment_stmt)
        latest_assessment = assessment_res.scalars().first()

        if latest_assessment:
            framework_data.append({"framework": fw, "assessment": latest_assessment})

    if not framework_data:
        return {"message": "No readiness assessments found."}

    # Calculate dashboard metrics
    total_frameworks = len(framework_data)
    avg_score = sum(f["assessment"].overall_score for f in framework_data) / total_frameworks if total_frameworks > 0 else 0

    # Extract priority actions and other details
    all_priority_actions = []
    for fw_data in framework_data:
        for action in fw_data["assessment"].priority_actions[:3]:
            all_priority_actions.append({
                "framework": fw_data["framework"].display_name,
                "action": action.get("action", "N/A"),
                "urgency": action.get("urgency", "N/A"),
                "impact": action.get("impact", "N/A")
            })

    return {
        "total_frameworks": total_frameworks,
        "average_score": round(avg_score, 2),
        "framework_scores": [
            {
                "name": fw["framework"].display_name,
                "score": fw["assessment"].overall_score,
                "trend": fw["assessment"].score_trend,
            } for fw in framework_data
        ],
        "priority_actions": all_priority_actions[:10]
    }
