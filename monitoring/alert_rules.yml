# Prometheus alert rules for ComplianceGPT
# These rules define when to trigger alerts based on application metrics

groups:
  - name: compliancegpt_alerts
    rules:
      # Application Health Alerts
      - alert: ApplicationDown
        expr: up{job="compliancegpt-api"} == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "ComplianceGPT API is down"
          description: "The ComplianceGPT API has been down for more than 1 minute."

      - alert: HighErrorRate
        expr: rate(http_requests_total{status=~"5.."}[5m]) / rate(http_requests_total[5m]) > 0.05
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High error rate detected"
          description: "Error rate is {{ $value | humanizePercentage }} for the last 5 minutes."

      - alert: SlowResponseTime
        expr: histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m])) > 2
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "Slow response times"
          description: "95th percentile response time is {{ $value }}s for the last 5 minutes."

      # Database Alerts
      - alert: DatabaseConnectionFailure
        expr: up{job="postgresql"} == 0
        for: 2m
        labels:
          severity: critical
        annotations:
          summary: "Database connection failure"
          description: "Unable to connect to PostgreSQL database for more than 2 minutes."

      - alert: HighDatabaseConnections
        expr: pg_stat_database_numbackends / pg_settings_max_connections > 0.8
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High database connection usage"
          description: "Database connection usage is {{ $value | humanizePercentage }} of maximum."

      - alert: DatabaseSlowQueries
        expr: rate(pg_stat_database_tup_fetched[5m]) / rate(pg_stat_database_tup_returned[5m]) > 100
        for: 10m
        labels:
          severity: warning
        annotations:
          summary: "Database queries are slow"
          description: "Database is fetching many rows per returned row, indicating inefficient queries."

      # Redis Alerts
      - alert: RedisDown
        expr: up{job="redis"} == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Redis is down"
          description: "Redis has been down for more than 1 minute."

      - alert: RedisHighMemoryUsage
        expr: redis_memory_used_bytes / redis_memory_max_bytes > 0.9
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "Redis high memory usage"
          description: "Redis memory usage is {{ $value | humanizePercentage }} of maximum."

      # Celery Worker Alerts
      - alert: CeleryWorkersDown
        expr: celery_workers_total == 0
        for: 2m
        labels:
          severity: critical
        annotations:
          summary: "No Celery workers running"
          description: "All Celery workers are down for more than 2 minutes."

      - alert: HighCeleryQueueLength
        expr: celery_queue_length > 100
        for: 10m
        labels:
          severity: warning
        annotations:
          summary: "High Celery queue length"
          description: "Celery queue {{ $labels.queue }} has {{ $value }} pending tasks."

      - alert: CeleryTaskFailureRate
        expr: rate(celery_task_failed_total[10m]) / rate(celery_task_total[10m]) > 0.1
        for: 10m
        labels:
          severity: warning
        annotations:
          summary: "High Celery task failure rate"
          description: "Celery task failure rate is {{ $value | humanizePercentage }} for the last 10 minutes."

      # System Resource Alerts
      - alert: HighCPUUsage
        expr: 100 - (avg by(instance) (rate(node_cpu_seconds_total{mode="idle"}[5m])) * 100) > 80
        for: 10m
        labels:
          severity: warning
        annotations:
          summary: "High CPU usage"
          description: "CPU usage is {{ $value }}% on {{ $labels.instance }}."

      - alert: HighMemoryUsage
        expr: (1 - (node_memory_MemAvailable_bytes / node_memory_MemTotal_bytes)) * 100 > 85
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High memory usage"
          description: "Memory usage is {{ $value }}% on {{ $labels.instance }}."

      - alert: LowDiskSpace
        expr: (1 - (node_filesystem_avail_bytes / node_filesystem_size_bytes)) * 100 > 85
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "Low disk space"
          description: "Disk usage is {{ $value }}% on {{ $labels.instance }} {{ $labels.mountpoint }}."

      # AI Service Alerts
      - alert: AIServiceHighErrorRate
        expr: rate(ai_requests_failed_total[10m]) / rate(ai_requests_total[10m]) > 0.1
        for: 10m
        labels:
          severity: warning
        annotations:
          summary: "High AI service error rate"
          description: "AI service error rate is {{ $value | humanizePercentage }} for the last 10 minutes."

      - alert: AIServiceSlowResponses
        expr: histogram_quantile(0.95, rate(ai_request_duration_seconds_bucket[5m])) > 30
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "Slow AI service responses"
          description: "95th percentile AI response time is {{ $value }}s for the last 5 minutes."

      # Report Generation Alerts
      - alert: ReportGenerationFailures
        expr: rate(report_generation_failed_total[10m]) > 0.1
        for: 10m
        labels:
          severity: warning
        annotations:
          summary: "High report generation failure rate"
          description: "Report generation is failing at {{ $value }} failures per second."

      - alert: SlowReportGeneration
        expr: histogram_quantile(0.95, rate(report_generation_duration_seconds_bucket[10m])) > 300
        for: 10m
        labels:
          severity: warning
        annotations:
          summary: "Slow report generation"
          description: "95th percentile report generation time is {{ $value }}s for the last 10 minutes."

  - name: business_logic_alerts
    rules:
      # Business Logic Alerts
      - alert: LowEvidenceCollectionRate
        expr: rate(evidence_collected_total[1h]) < 1
        for: 2h
        labels:
          severity: info
        annotations:
          summary: "Low evidence collection rate"
          description: "Evidence collection rate is below 1 item per hour for the last 2 hours."

      - alert: NoScheduledReportsExecuted
        expr: increase(scheduled_reports_executed_total[24h]) == 0
        for: 25h
        labels:
          severity: warning
        annotations:
          summary: "No scheduled reports executed"
          description: "No scheduled reports have been executed in the last 24 hours."

      - alert: HighUserErrorRate
        expr: rate(user_errors_total[10m]) > 0.5
        for: 10m
        labels:
          severity: warning
        annotations:
          summary: "High user error rate"
          description: "Users are experiencing errors at {{ $value }} errors per second."

      - alert: IntegrationConnectionFailures
        expr: increase(integration_connection_failures_total[1h]) > 10
        for: 1h
        labels:
          severity: warning
        annotations:
          summary: "High integration connection failures"
          description: "{{ $value }} integration connection failures in the last hour."