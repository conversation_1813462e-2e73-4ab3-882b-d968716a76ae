# Prometheus configuration for ComplianceGPT monitoring
# This file configures Prometheus to scrape metrics from the application and its components

global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  - "alert_rules.yml"

alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093

scrape_configs:
  # ComplianceGPT API metrics
  - job_name: 'compliancegpt-api'
    static_configs:
      - targets: ['app:8000']
    metrics_path: '/metrics'
    scrape_interval: 10s
    scrape_timeout: 5s

  # Redis metrics
  - job_name: 'redis'
    static_configs:
      - targets: ['redis:6379']

  # PostgreSQL metrics (requires postgres_exporter)
  - job_name: 'postgresql'
    static_configs:
      - targets: ['postgres_exporter:9187']

  # Celery metrics (requires celery-prometheus-exporter)
  - job_name: 'celery'
    static_configs:
      - targets: ['celery_exporter:8888']

  # Node/system metrics
  - job_name: 'node'
    static_configs:
      - targets: ['node_exporter:9100']

  # Prometheus itself
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']