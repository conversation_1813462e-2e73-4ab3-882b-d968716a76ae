"""
Usability Testing Framework for ComplianceGPT

This module tests user experience, interface usability, and workflow
intuitiveness for the compliance automation platform.
"""


import pytest


@pytest.mark.usability
class TestUserOnboardingFlow:
    """Test user onboarding experience and ease of use"""

    def test_registration_simplicity(self, client):
        """Test that user registration is simple and intuitive"""
        # Test minimal required fields
        simple_registration = {
            "email": "<EMAIL>",
            "password": "SimplePassword123!",
            "full_name": "Simple User"
        }

        response = client.post("/api/auth/register", json=simple_registration)
        assert response.status_code == 201, "Simple registration should succeed"

        # Verify response provides clear next steps
        response_data = response.json()
        assert "email" in response_data
        assert "welcome_message" in response_data or "next_steps" in response_data

    def test_business_profile_guided_creation(self, client, authenticated_headers):
        """Test that business profile creation provides guidance"""
        # Test profile creation with help text and validation
        response = client.get("/api/business-profiles/create-wizard", headers=authenticated_headers)

        if response.status_code == 200:
            wizard_data = response.json()

            # Verify wizard provides guidance
            assert "steps" in wizard_data
            assert "help_text" in wizard_data
            assert "estimated_time" in wizard_data

            # Verify reasonable number of steps
            assert len(wizard_data["steps"]) <= 5, "Should not overwhelm users with too many steps"

    def test_assessment_question_clarity(self, client, authenticated_headers):
        """Test that assessment questions are clear and well-explained"""
        # Create assessment session
        session_response = client.post(
            "/api/assessments",
            headers=authenticated_headers,
            json={"session_type": "compliance_scoping"}
        )
        session_id = session_response.json()["id"]

        # Get questions
        questions_response = client.get(
            f"/api/assessments/{session_id}/questions",
            headers=authenticated_headers
        )

        if questions_response.status_code == 200:
            questions_data = questions_response.json()
            questions = questions_data.get("questions", [])

            for question in questions[:3]:  # Test first few questions
                # Verify question clarity
                assert len(question["question"]) >= 20, "Questions should be descriptive"
                assert len(question["question"]) <= 200, "Questions should not be overwhelming"

                # Verify help text exists
                assert "help_text" in question or "description" in question

                # For multiple choice, verify reasonable options
                if question.get("type") == "multiple_choice":
                    options = question.get("options", [])
                    assert 2 <= len(options) <= 6, "Should have reasonable number of options"


@pytest.mark.usability
class TestNavigationAndWorkflow:
    """Test navigation intuitiveness and workflow efficiency"""

    def test_dashboard_information_hierarchy(self, client, authenticated_headers):
        """Test that dashboard presents information in logical hierarchy"""
        response = client.get("/api/dashboard", headers=authenticated_headers)

        if response.status_code == 200:
            dashboard_data = response.json()

            # Verify key sections exist
            expected_sections = [
                "compliance_status", "recent_activity", "next_actions",
                "progress_overview", "recommendations"
            ]

            present_sections = sum(1 for section in expected_sections
                                 if section in dashboard_data)
            assert present_sections >= 3, "Dashboard should have key information sections"

            # Verify progress indicators are intuitive
            if "compliance_status" in dashboard_data:
                status = dashboard_data["compliance_status"]
                assert "overall_score" in status
                assert 0 <= status["overall_score"] <= 100
                assert "visual_indicator" in status or "status_text" in status

    def test_compliance_journey_breadcrumbs(self, client, authenticated_headers):
        """Test that users can understand their progress in compliance journey"""
        # Test assessment progress tracking
        session_response = client.post(
            "/api/assessments",
            headers=authenticated_headers,
            json={"session_type": "compliance_scoping"}
        )
        session_id = session_response.json()["id"]

        progress_response = client.get(
            f"/api/assessments/{session_id}/progress",
            headers=authenticated_headers
        )

        if progress_response.status_code == 200:
            progress_data = progress_response.json()

            # Verify progress indicators
            assert "current_stage" in progress_data
            assert "total_stages" in progress_data
            assert "completion_percentage" in progress_data
            assert "next_step" in progress_data

            # Verify reasonable progress tracking
            assert 0 <= progress_data["completion_percentage"] <= 100

    def test_action_feedback_and_confirmation(self, client, authenticated_headers, mock_ai_client):
        """Test that user actions provide clear feedback"""
        mock_ai_client.generate_content.return_value.text = "Sample policy content"

        # Test policy generation feedback
        frameworks_response = client.get("/api/frameworks", headers=authenticated_headers)
        if frameworks_response.status_code == 200:
            framework_id = frameworks_response.json()["frameworks"][0]["id"]

            policy_response = client.post(
                "/api/policies/generate",
                headers=authenticated_headers,
                json={"framework_id": framework_id}
            )

            if policy_response.status_code == 201:
                policy_data = policy_response.json()

                # Verify clear status communication
                assert "status" in policy_data
                assert "message" in policy_data or "success_message" in policy_data

                # Verify next steps guidance
                assert "next_steps" in policy_data or "recommended_actions" in policy_data


@pytest.mark.usability
class TestContentReadabilityAndClarity:
    """Test readability and clarity of generated content"""

    def test_policy_content_readability(self, client, authenticated_headers, mock_ai_client):
        """Test that generated policies are readable and well-structured"""
        mock_ai_client.generate_content.return_value.text = """
        # Data Protection Policy

        ## 1. Purpose
        This policy helps our company protect personal data and comply with GDPR.

        ## 2. What is Personal Data?
        Personal data means any information about a person who can be identified.

        ## 3. Our Commitments
        We promise to:
        - Only collect data we need
        - Keep data secure
        - Let people know how we use their data
        """

        frameworks_response = client.get("/api/frameworks", headers=authenticated_headers)
        if frameworks_response.status_code == 200:
            framework_id = frameworks_response.json()["frameworks"][0]["id"]

            policy_response = client.post(
                "/api/policies/generate",
                headers=authenticated_headers,
                json={"framework_id": framework_id}
            )

            if policy_response.status_code == 201:
                policy_content = policy_response.json()["content"]

                # Test readability metrics
                self._assert_content_readability(policy_content)

    def test_recommendation_clarity(self, client, authenticated_headers, sample_business_profile):
        """Test that recommendations are clear and actionable"""
        # Create business profile
        client.post(
            "/api/business-profiles",
            headers=authenticated_headers,
            json=sample_business_profile
        )

        # Get recommendations
        recommendations_response = client.post(
            "/api/frameworks/recommend",
            headers=authenticated_headers,
            json=sample_business_profile
        )

        if recommendations_response.status_code == 200:
            recommendations = recommendations_response.json()["recommendations"]

            for rec in recommendations[:3]:  # Test first few recommendations
                # Verify clear explanations
                assert "reasons" in rec
                assert len(rec["reasons"]) >= 1

                # Verify actionable information
                assert "priority" in rec
                assert rec["priority"] in ["High", "Medium", "Low"]

                # Verify framework information is clear
                framework = rec["framework"]
                assert "display_name" in framework
                assert "description" in framework
                assert len(framework["description"]) >= 50, "Description should be informative"

    def test_error_message_helpfulness(self, client, authenticated_headers):
        """Test that error messages are helpful and guide users toward solutions"""
        # Test validation error messages
        invalid_profile = {
            "company_name": "",  # Invalid
            "industry": "x",      # Too short
            "employee_count": -1  # Invalid
        }

        response = client.post(
            "/api/business-profiles",
            headers=authenticated_headers,
            json=invalid_profile
        )

        assert response.status_code == 422
        error_data = response.json()

        # Verify helpful error messages
        assert "error" in error_data
        error_details = error_data["error"]

        if "details" in error_details:
            validation_errors = error_details["details"].get("validation_errors", [])

            for error in validation_errors:
                # Error messages should be specific and helpful
                assert "field" in error or "loc" in error
                assert "msg" in error or "message" in error

                # Messages should guide users
                error_text = error.get("msg", error.get("message", "")).lower()
                helpful_indicators = [
                    "required", "must be", "should be", "minimum", "maximum",
                    "invalid", "format", "length", "between"
                ]

                has_helpful_indicator = any(indicator in error_text for indicator in helpful_indicators)
                assert has_helpful_indicator, f"Error message should be helpful: {error_text}"

    def _assert_content_readability(self, content: str):
        """Assert that content meets readability standards"""
        # Basic readability checks
        sentences = content.split('.')
        avg_sentence_length = sum(len(s.split()) for s in sentences) / len(sentences) if sentences else 0

        # Sentences shouldn't be too long
        assert avg_sentence_length <= 25, "Sentences should be reasonably short for readability"

        # Should have proper structure with headings
        assert content.count('#') >= 3, "Content should have clear structure with headings"

        # Should avoid jargon without explanation
        jargon_terms = ['gdpr', 'dpo', 'ico', 'supervisory authority']
        for term in jargon_terms:
            if term in content.lower():
                # If jargon is used, it should be explained or expanded
                term_context = content.lower().find(term)
                if term_context != -1:
                    surrounding_text = content[max(0, term_context-50):term_context+100].lower()
                    _ = any(indicator in surrounding_text for indicator in [
                        'means', 'refers to', 'is', 'stands for', '(', 'also known as'
                    ])
                    # Note: Not asserting this as it depends on context


@pytest.mark.usability
class TestAccessibilityAndInclusion:
    """Test accessibility and inclusive design principles"""

    def test_api_response_structure_consistency(self, client, authenticated_headers):
        """Test that API responses have consistent structure for UI consistency"""
        endpoints_to_test = [
            "/api/frameworks",
            "/api/business-profiles",
            "/api/assessments"
        ]

        for endpoint in endpoints_to_test:
            response = client.get(endpoint, headers=authenticated_headers)

            if response.status_code == 200:
                response_data = response.json()

                # Verify consistent error handling structure
                if "error" in response_data:
                    assert "message" in response_data["error"]
                    assert "code" in response_data["error"]

                # Verify pagination consistency for list endpoints
                if isinstance(response_data, dict) and any(key.endswith('s') for key in response_data):
                    # This is likely a list endpoint
                    list_key = next((key for key in response_data if key.endswith('s')), None)
                    if list_key:
                        assert isinstance(response_data[list_key], list)

    def test_internationalization_support(self, client, authenticated_headers):
        """Test support for international users and data"""
        # Test with international business data
        international_profile = {
            "company_name": "Société Exemple SARL",
            "industry": "Technology",
            "employee_count": 25,
            "country": "FR",
            "handles_personal_data": True,
            "has_international_operations": True
        }

        response = client.post(
            "/api/business-profiles",
            headers=authenticated_headers,
            json=international_profile
        )

        if response.status_code == 201:
            # Verify system can handle international data
            profile_data = response.json()
            assert profile_data["company_name"] == international_profile["company_name"]
            assert profile_data["country"] == "FR"

    def test_mobile_friendly_response_structure(self, client, authenticated_headers):
        """Test that responses are structured for mobile consumption"""
        response = client.get("/api/dashboard", headers=authenticated_headers)

        if response.status_code == 200:
            dashboard_data = response.json()

            # Verify responses include summary/condensed views suitable for mobile
            mobile_friendly_indicators = [
                "summary", "condensed_view", "mobile_layout",
                "key_metrics", "highlights", "overview"
            ]

            _ = any(indicator in dashboard_data
                    for indicator in mobile_friendly_indicators)

            # Note: This is more of a design guideline than a hard requirement


@pytest.mark.usability
class TestUserGuidanceAndHelp:
    """Test user guidance, help systems, and learning support"""

    def test_contextual_help_availability(self, client, authenticated_headers):
        """Test that contextual help is available throughout the application"""
        # Test help endpoint existence
        help_response = client.get("/api/help/getting-started", headers=authenticated_headers)

        # Help system should be available (even if content is basic)
        assert help_response.status_code in [200, 501], "Help system should be implemented or planned"

        if help_response.status_code == 200:
            help_data = help_response.json()
            assert "content" in help_data or "help_items" in help_data

    def test_progress_indicators_and_motivation(self, client, authenticated_headers, sample_business_profile):
        """Test that users receive clear progress indicators and motivation"""
        # Create business profile
        client.post(
            "/api/business-profiles",
            headers=authenticated_headers,
            json=sample_business_profile
        )

        # Check progress tracking
        progress_response = client.get("/api/compliance/progress", headers=authenticated_headers)

        if progress_response.status_code == 200:
            progress_data = progress_response.json()

            # Verify motivational elements
            motivational_elements = [
                "achievements", "milestones", "progress_percentage",
                "completed_tasks", "next_milestone", "congratulations"
            ]

            present_elements = sum(1 for element in motivational_elements
                                 if element in progress_data)
            assert present_elements >= 2, "Should provide motivational progress indicators"

    def test_workflow_guidance_and_next_steps(self, client, authenticated_headers):
        """Test that users always know what to do next"""
        # Test dashboard provides next steps
        dashboard_response = client.get("/api/dashboard", headers=authenticated_headers)

        if dashboard_response.status_code == 200:
            dashboard_data = dashboard_response.json()

            # Verify next steps guidance
            guidance_indicators = [
                "next_steps", "recommended_actions", "todo_items",
                "action_items", "next_action", "suggestions"
            ]

            has_guidance = any(indicator in dashboard_data for indicator in guidance_indicators)
            assert has_guidance, "Dashboard should provide clear next steps"

    def test_complexity_progressive_disclosure(self, client, authenticated_headers):
        """Test that complex information is progressively disclosed"""
        frameworks_response = client.get("/api/frameworks", headers=authenticated_headers)

        if frameworks_response.status_code == 200:
            frameworks = frameworks_response.json()["frameworks"]

            if frameworks:
                framework = frameworks[0]
                framework_id = framework["id"]

                # Basic framework info should be simple
                basic_fields = ["name", "display_name", "description"]
                for field in basic_fields:
                    assert field in framework
                    if field == "description":
                        # Description should be concise in list view
                        assert len(framework[field]) <= 300, "List descriptions should be concise"

                # Detailed view should have more information
                detail_response = client.get(f"/api/frameworks/{framework_id}", headers=authenticated_headers)

                if detail_response.status_code == 200:
                    detail_data = detail_response.json()

                    # Detail view should have more comprehensive information
                    detailed_fields = ["requirements", "implementation_guidance", "timeline"]
                    present_detailed = sum(1 for field in detailed_fields if field in detail_data)
                    assert present_detailed >= 1, "Detail view should provide more comprehensive information"


@pytest.mark.usability
class TestUserWorkflowEfficiency:
    """Test workflow efficiency and task completion ease"""

    def test_bulk_operations_support(self, client, authenticated_headers):
        """Test support for bulk operations where appropriate"""
        # Test bulk status updates for implementation tasks
        bulk_update_response = client.patch(
            "/api/implementation/tasks/bulk-update",
            headers=authenticated_headers,
            json={
                "task_ids": ["task1", "task2", "task3"],
                "status": "completed",
                "bulk_notes": "Completed batch of related tasks"
            }
        )

        # Bulk operations should be supported or return a helpful message
        assert bulk_update_response.status_code in [200, 501, 400], "Should handle bulk operations appropriately"

    def test_quick_actions_availability(self, client, authenticated_headers):
        """Test availability of quick actions for common tasks"""
        quick_actions_response = client.get("/api/quick-actions", headers=authenticated_headers)

        if quick_actions_response.status_code == 200:
            quick_actions = quick_actions_response.json()

            # Verify common quick actions
            expected_actions = [
                "complete_assessment", "generate_policy", "update_compliance_status",
                "schedule_review", "export_report"
            ]

            if "actions" in quick_actions:
                available_actions = [action["id"] for action in quick_actions["actions"]]
                matching_actions = sum(1 for action in expected_actions if action in available_actions)
                assert matching_actions >= 2, "Should provide common quick actions"

    def test_save_and_resume_functionality(self, client, authenticated_headers):
        """Test that users can save progress and resume later"""
        # Create assessment session
        session_response = client.post(
            "/api/assessments",
            headers=authenticated_headers,
            json={"session_type": "compliance_scoping"}
        )

        if session_response.status_code == 201:
            session_id = session_response.json()["id"]

            # Submit partial response
            partial_response = client.post(
                f"/api/assessments/{session_id}/responses",
                headers=authenticated_headers,
                json={
                    "question_id": "data_processing",
                    "response": "partial answer",
                    "save_progress": True
                }
            )

            assert partial_response.status_code == 200, "Should allow saving partial progress"

            # Verify session can be resumed
            resume_response = client.get(
                f"/api/assessments/{session_id}",
                headers=authenticated_headers
            )

            if resume_response.status_code == 200:
                session_data = resume_response.json()
                assert session_data["status"] in ["in_progress", "draft"], "Session should be resumable"
