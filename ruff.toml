# Ruff configuration for ComplianceGPT
target-version = "py38"
line-length = 100
select = [
    "E",   # pycodestyle errors
    "W",   # pycodestyle warnings
    "F",   # Pyflakes
    "I",   # isort
    "N",   # pep8-naming
    "UP",  # pyupgrade
    "B",   # flake8-bugbear
    "C4",  # flake8-comprehensions
    "SIM", # flake8-simplify
    "RUF", # Ruff-specific rules
]

ignore = [
    "E501",  # Line too long (handled by line-length)
    "B008",  # Do not perform function calls in argument defaults
    "B904",  # Within an except clause, raise exceptions with raise ... from err
    "N806",  # Variable should be lowercase
    "N802",  # Function name should be lowercase
    "SIM102", # Use a single if-statement
    "SIM108", # Use ternary operator
    "W291",  # Trailing whitespace
    "W293",  # Blank line contains whitespace
]

# Directories to exclude
exclude = [
    ".git",
    "__pycache__",
    ".venv",
    "venv",
    ".env",
    "env",
    "build",
    "dist",
    "htmlcov",
    ".pytest_cache",
    ".mypy_cache",
    ".ruff_cache",
    "migrations",
]

[per-file-ignores]
"tests/*" = ["SIM117", "B011"]
"*/__init__.py" = ["F401"]  # Allow unused imports in __init__.py

[isort]
known-first-party = ["api", "database", "services", "config", "core"]
section-order = ["future", "standard-library", "third-party", "first-party", "local-folder"]

[format]
quote-style = "double"
indent-style = "space"
line-ending = "auto"