version: '3.8'

services:
  app:
    build: .
    ports:
      - "8000:8000"
    environment:
      - ENV=development
      - DATABASE_URL=postgresql+psycopg2://postgres:postgres@db:5432/compliancegpt
      - REDIS_URL=redis://redis:6379/0
      - GOOGLE_API_KEY=${GOOGLE_API_KEY}
      - ALLOWED_ORIGINS=http://localhost:3000,http://localhost:8080
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_healthy
    volumes:
      - .:/app
    restart: unless-stopped
    networks:
      - compliancegpt-network

  celery_worker:
    build: .
    command: celery -A celery_app worker --loglevel=info --concurrency=2 --queues=evidence,compliance,notifications,reports
    environment:
      - ENV=development
      - DATABASE_URL=postgresql+psycopg2://postgres:postgres@db:5432/compliancegpt
      - REDIS_URL=redis://redis:6379/0
      - GOOGLE_API_KEY=${GOOGLE_API_KEY}
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_healthy
    volumes:
      - .:/app
    restart: unless-stopped
    networks:
      - compliancegpt-network

  celery_beat:
    build: .
    command: celery -A celery_app beat --loglevel=info
    environment:
      - ENV=development
      - DATABASE_URL=postgresql+psycopg2://postgres:postgres@db:5432/compliancegpt
      - REDIS_URL=redis://redis:6379/0
      - GOOGLE_API_KEY=${GOOGLE_API_KEY}
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_healthy
    volumes:
      - .:/app
    restart: unless-stopped
    networks:
      - compliancegpt-network

  db:
    image: postgres:15-alpine
    environment:
      - POSTGRES_DB=compliancegpt
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database/init.sql:/docker-entrypoint-initdb.d/init.sql
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - compliancegpt-network

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - compliancegpt-network

volumes:
  postgres_data:
  redis_data:

networks:
  compliancegpt-network:
    driver: bridge