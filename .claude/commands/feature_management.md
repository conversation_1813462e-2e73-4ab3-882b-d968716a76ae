# Feature & Audit Management Commands

## process_feature_request

Submit and process a new feature request

```prompt
Process a feature request for "$TITLE".
Feature description: $DESCRIPTION
Priority: $PRIORITY
Target release: $VERSION
Evaluate feasibility, scope, and technical requirements.
Create detailed implementation plan with subtasks and timeline.
```

## process_audit_findings

Convert audit findings into actionable items

```prompt
Process audit findings from $INPUT.
Convert each finding into specific, actionable tasks.
Prioritize tasks by severity and impact.
Create a remediation plan with timeline and resource requirements.
```

## generate_audit_report

Generate comprehensive audit report

```prompt
Generate a $SCOPE audit report.
Include security, performance, accessibility, and code quality assessments.
Provide detailed findings with severity ratings and remediation recommendations.
Organize report by category with executive summary and detailed analysis.
```

## track_implementation

Track feature implementation progress

```prompt
Track implementation progress for feature "$TITLE".
Assess current status against planned milestones.
Identify blocking issues and dependencies.
Provide recommendations for addressing delays or technical challenges.
``` 