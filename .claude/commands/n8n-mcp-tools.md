{"title": "n8n MCP Tools", "description": "Use n8n, QDRANT, and Supabase MCP servers to work with n8n workflows", "prompt": "You are now using the n8n-templates project which includes integrations with several MCP servers: n8n, QDRANT, and Supabase. This command provides context and functionality to work with these integrations.\n\nThe project is located at /Users/<USER>/Projects/n8n-templates and serves as a tool for analyzing, categorizing, and storing n8n workflows across multiple database types.\n\nKey MCP integrations available:\n\n1. n8n MCP Server: Connect to n8n instance and manage workflows\n   - List workflows with `npm run n8n-demo`\n   - Import workflows with `npm run import-all` or `npm run import <workflow-id>`\n\n2. QDRANT MCP Server: Vector search and similarity matching\n   - Vectorize workflows with `npm run vectorize-mcp`\n   - Search similar workflows semantically\n\n3. Supabase MCP Server: Structured data storage\n   - Store workflow metadata with `npm run supabase-mcp`\n   - Query workflow data through Supabase\n\nKey project files:\n- workflow-parser.js: Process and extract metadata from n8n workflows\n- vectorize-workflows.js: Workflow vectorization utilities\n- n8n-mcp-client.js: Client for n8n MCP server\n- import-to-n8n.js: Import processed workflows to n8n\n- mcp-client-config.js: Configuration for MCP clients\n\nEnvironment variables used for MCP connections are stored in .env file.\n\nWhen working with this project, I can help you:\n- Parse and process n8n workflows\n- Connect to n8n, QDRANT, or Supabase servers via MCP\n- Import workflows to a running n8n instance\n- Create vector embeddings for semantic search\n- Query and search workflows across databases\n- Develop new MCP integration features\n\nBased on the above context, I am now ready to help you work with the n8n-templates project and its MCP integrations. What specific aspect of the MCP tools would you like to work with?", "completion_prompt": "This prompt provides <PERSON> with context about the n8n-templates project and its MCP integrations. <PERSON> can now assist with n8n workflow processing, vector search using QDRANT, and data storage with Supabase, all through MCP servers."}