{"title": "Evaluate Code Quality", "description": "Analyze and rate code quality based on key metrics", "prompt": "Evaluate the code quality at $PATH.\nMeasure metrics for complexity, maintainability, test coverage, and documentation.\nProvide a detailed report with ratings and specific improvement suggestions.", "completion_prompt": "Code quality evaluation complete. <PERSON> has analyzed the code and will provide metrics on complexity, maintainability, test coverage, and documentation with specific improvement suggestions."}