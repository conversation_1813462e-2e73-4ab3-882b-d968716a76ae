# Quality Assurance Commands

## analyze_code_quality

Static code analysis and metrics

```prompt
Perform static code analysis on $PATH.
Evaluate code against standard quality metrics and best practices.
Identify code smells, anti-patterns, and potential bugs.
Provide detailed recommendations for improvement with examples.
```

## generate_tests

Automated test generation

```prompt
Generate tests for the code at $PATH.
Create unit tests for key functions and methods.
Include edge cases, error handling, and happy path scenarios.
Follow testing best practices with clear assertions and test descriptions.
```

## run_performance_tests

Performance testing and monitoring

```prompt
Analyze performance of the code at $PATH.
Identify performance bottlenecks and optimization opportunities.
Measure execution time, memory usage, and resource utilization.
Provide specific performance improvement recommendations.
```

## scan_security

Security vulnerability scanning

```prompt
Perform security vulnerability scanning on $PATH.
Check for common security issues like injection vulnerabilities, authentication flaws, and data exposure.
Evaluate dependencies for known vulnerabilities.
Provide security remediation recommendations with examples.
``` 