# UI/UX & Design Commands

## interpret_ui

Initialize UI interpretation workflow

```prompt
Interpret the UI $INPUT and convert to code components.
Use $STYLE design system for the implementation.
Provide clean, accessible, and responsive component code.
```

## analyze_ui

Analyze UI design and implementation

```prompt
Analyze the UI design at $PATH.
Evaluate the design for accessibility, usability, and consistency.
Provide specific improvement recommendations with code examples.
```

## compare_designs

Compare design system implementations

```prompt
Compare the current design at $CURRENT with target design at $TARGET.
Identify differences in styling, components, and layout.
Provide a migration plan to align the current design with the target.
```

## generate_ui_components

Generate UI components from specifications

```prompt
Generate UI components based on the specifications at $PATH.
Implement components using $FRAMEWORK and $DESIGN_SYSTEM.
Include responsive behavior, accessibility features, and comprehensive documentation.
``` 