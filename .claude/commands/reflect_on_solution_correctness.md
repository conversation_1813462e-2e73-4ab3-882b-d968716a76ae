# Reflect on Solution Correctness

${PREVIOUS_RESPONSES}

Please carefully review your previous ${NUMBER OF PERVIOUS} response and identify any issues: mistakes, inconsistencies, completeness, incorrect information, knowledge gaps, logic gaps, code smells, or other shortcomings. Start by understanding the problem. If you find any issues, please follow these steps:

## Step 1

For each issue you identify, describe it in detail within <issue> tags. Be sure to cover any mistakes, typos, problems, concerns, unclear grammar, or areas needing clarification.

## Step 2

For each <issue>, come up with 3 distinct potential solutions. List each solution within its own <solution> tags. Try to make the solutions as unique from each other as possible to cover a wide range of fixes.

## Step 3

Compare the efficiency of the 3 <solution>s for each <issue>. Analyze factors such as:

- The time and resources needed to implement the solution
- The potential impact of the solution on the overall response
- The probability that the solution will successfully resolve the issue
Discuss this comparion within <efficiency_comparison> tags.

## Step 4

Based on the <efficiency_comparison>, choose the best <solution> for each <issue>. Explain your selection within <best_solution> tags.

## Step 5

Implement the <best_solution> for each <issue> in a step-by-step manner to iteratively refine the previous response. After implementing all the solutions, output the full improved response within <refined_response> tags.

## Step 6

Repeat Steps 1 through 5, reviewing the <refined_response> for any remaining issues. Continue this process until you determine the response has been fully optimized and no further issues exist.

## Step 7

If there are issues, and you end up finding an acceptable solution that addresses the issue completely that the user agrees with, update your memory in the scratch pad (.context/scratch_pad.md) by creating a STRICT rule to follow these learnings that the issue(s) do not repeat.
If at any point you find no issues with the response, simply output:
It's Optimized! No more issues :) star

## IMPORTANT!!

Always think carefully and deeply before responding. Use first principle and critical analytical thinking to ensure the information you provide is correct and will lead to the most optimal response.
