# Sprint Management Commands

## init_sprint

Initialize sprint planning and setup

```prompt
Initialize a new sprint for the project.
Define sprint goals, duration, and capacity.
Set up sprint tracking structure and documentation.
```

## generate_sprint_stories

Generate and manage sprint user stories

```prompt
Generate user stories for the upcoming sprint.
Base stories on project roadmap and priorities.
Include acceptance criteria, story points, and dependencies for each story.
Organize stories by feature area and technical complexity.
```

## analyze_stories

Analyze and validate user stories

```prompt
Analyze the user stories for the current sprint.
Validate each story for clarity, completeness, and technical feasibility.
Identify missing details, ambiguities, and potential implementation challenges.
Provide recommendations for story refinement and prioritization.
```

## decompose_stories

Break down complex stories into tasks

```prompt
Decompose complex user stories into specific implementation tasks.
Break down story "$STORY" into technical subtasks.
Estimate effort for each task and identify dependencies.
Provide implementation guidance for complex or high-risk tasks.
```

## track_sprint_implementation

Track sprint implementation progress

```prompt
Track implementation progress for the current sprint.
Assess story completion against sprint timeline.
Identify blockers, dependencies, and scope changes.
Provide recommendations for sprint adjustments if needed.
``` 