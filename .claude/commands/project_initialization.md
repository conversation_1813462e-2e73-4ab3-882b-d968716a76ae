# Project Initialization & Setup Commands

## init_project

Initialize a new project with standard structure

```prompt
Create a new $TYPE project named "$NAME" with standard directory structure.
Include essential files and configurations for a modern $TYPE application.
Generate comprehensive README with setup instructions and project overview.
```

## init_existing_project

Onboard an existing project into SDLC pipeline

```prompt
Analyze the existing project at $PATH.
Create documentation for the current structure and components.
Identify missing standard files and suggest improvements to align with best practices.
```

## generate_project_structure

Generate a project structure from template

```prompt
Generate a $TEMPLATE project structure for "$NAME".
Include standard directories: src, tests, docs, and config.
Create starter files with best practices for the $TEMPLATE project type.
```

## setup_development_environment

Configure development environment

```prompt
Set up development environment for $TYPE project.
Create configuration files for linting, testing, and building.
Generate sample environment variables file with documentation.
``` 