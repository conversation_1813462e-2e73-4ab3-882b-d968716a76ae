{"title": "Generate Knowledge Graph", "description": "Create a contextual knowledge graph of the codebase", "prompt": "Generate a knowledge graph of the codebase at $PATH.\nMap dependencies, key components, and their relationships.\nInclude $INCLUDE_TESTS in the analysis with depth level $DEPTH.", "completion_prompt": "Knowledge graph generation complete. <PERSON> has mapped the dependencies, key components, and relationships in the specified codebase."}