# Context Management Commands

## init_context

Initialize context management system

```prompt
Initialize context management for this project.
Create a codebase context specification document.
Establish baseline understanding of project structure and components.
```

## analyze_context

Analyze and track project context changes

```prompt
Analyze the current project context.
Identify recent changes and their impact on the overall system.
Track dependency relationships and affected components.
Provide a context change impact assessment.
```

## update_context

Update project context documentation

```prompt
Update the project context documentation.
Incorporate recent changes to $COMPONENT.
Refresh component relationships and dependencies.
Ensure documentation alignment with current implementation.
```

## generate_context_docs

Generate comprehensive context documentation

```prompt
Generate context documentation for the project.
Create visual representation of component relationships.
Document key abstractions, patterns, and architectural decisions.
Include detailed component interaction flows and data models.
``` 