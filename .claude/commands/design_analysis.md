# Design Analysis Commands

## analyze_design_system

Analyze design system alignment with best practices

```prompt
Analyze the design system at $PATH.
Evaluate alignment with atomic design principles (atoms, molecules, organisms, templates).
Check token naming conventions, hierarchy, and cross-level referencing.
Assess design token implementation across global CSS, component CSS, and page CSS.
Provide specific recommendations for improving design system consistency and maintainability.
```

## evaluate_design_quality

Evaluate design quality against rubric standards

```prompt
Evaluate the design quality at $PATH using design principle rubric.
Assess color palette, layout/grid, typography, hierarchy/navigation, accessibility, and spacing/alignment.
Grade each category from A-F based on implementation quality.
Provide specific improvement recommendations with code examples for each category.
```

## analyze_design_thinking

Analyze design implementation using design thinking framework

```prompt
Analyze the $COMPONENT_TYPE at $PATH using design thinking framework.
Evaluate user-centered design implementation across empathy, problem definition, ideation, and prototyping.
Assess alignment with user research, personas, problem statements, and solution validation.
Recommend improvements to enhance user experience and design consistency.
```

## audit_design_tokens

Audit design token implementation and consistency

```prompt
Audit design tokens at $PATH.
Verify token hierarchy from atoms (colors, typography, spacing) to molecules, organisms, and templates.
Check cross-platform consistency across web, mobile, and other platforms.
Identify inconsistencies in token usage and naming conventions.
Provide recommendations for standardizing token implementation.
```

## analyze_css_architecture

Analyze CSS architecture alignment with design system

```prompt
Analyze the CSS architecture at $PATH.
Evaluate CSS organization across global styles, components, and pages.
Check alignment with design token usage and naming conventions.
Identify CSS inconsistencies, specificity issues, and maintenance challenges.
Recommend improvements for better CSS architecture and design system integration.
```

## comprehensive_design_review

Comprehensive design system and implementation review

```prompt
Perform a comprehensive design review of $PATH.
Evaluate design system implementation across token structure, CSS architecture, and component usage.
Assess alignment with design principles including accessibility, consistency, and user-centered design.
Analyze design thinking implementation through user journeys and interaction patterns.
Provide prioritized recommendations for improving design quality, consistency, and maintainability.
``` 