{"title": "Context Prime", "description": "Analyze the project's structure and status by examining README and files", "prompt": "READ README.md, THEN run git ls–files to understand the context of the project.\n\nYou are tasked with analyzing a current software project's file structure and current state and status. Follow these steps carefully:\n\n1. Read the content of the README.md file:\n\n2. Examine the content of files from the /.context directory for project status context\n\n3. review the output of running the git ls-files command:\n\n4. Carefully read and analyze all the information provided above. Pay attention to:\n\n- Project description and purpose in the README\n- Any setup instructions or dependencies mentioned\n- The current state of the project as reflected in the context files\n- The overall file structure and organization of the project\n\n5.Summarize the project's current status and state based on the information you've analyzed. Consider:\n\n- The main features or functionality of the project\n- The development stage (e.g., early development, beta, production-ready)\n- Any ongoing work or known issues mentioned in the context files\n\n6.Provide insights based on the file structure:\n\n- Identify the main components or modules of the project\n- Note any patterns in file organization\n- Highlight any interesting or unusual aspects of the project structure\n\n7.Format your final output as follows:\n\na. Project Overview: A brief description of the project and its purpose\nb. Current Status: A summary of the project's current state and development stage\nc. Key Components: List of main components or modules identified\nd. File Structure Insights: Notable observations about the project's organization\ne. Next Steps or Recommendations: Suggestions for potential improvements or areas of focus based on your analysis\n\nYour final output should be concise yet informative, providing a clear picture of the project's current state and structure. Include only the final analysis in your response, formatted as specified above. Do not include any of the raw input data or intermediate analysis steps in your output.", "completion_prompt": "Context priming complete. <PERSON> has analyzed the project structure and will provide a concise summary organized into Project Overview, Current Status, Key Components, File Structure Insights, and Next Steps."}