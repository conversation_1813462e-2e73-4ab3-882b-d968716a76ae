# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Virtual environments
venv/
env/
ENV/

# IDE
.vscode/
.idea/
*.swp
*.swo

# OS
.DS_Store
Thumbs.db

# Git
.git/
.gitignore

# Documentation
*.md
!README.md

# Logs
*.log
logs/

# Environment variables
.env
.env.local
.env.production

# Testing
.coverage
.pytest_cache/
htmlcov/

# Docker
Dockerfile*
docker-compose*
.dockerignore